import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository, FindOptionsWhere, Like } from 'typeorm'
import { ConfigService } from '@nestjs/config'

import { User, UserStatus, UserType } from '../entities/user.entity'
import { UserProfile } from '../entities/user-profile.entity'
import { UserPreference } from '../entities/user-preference.entity'
import { UserPrivacy } from '../entities/user-privacy.entity'
import { UserEducation } from '../entities/user-education.entity'

import { CreateUserDto } from '../dto/create-user.dto'
import { UpdateUserDto } from '../dto/update-user.dto'
import { UserQueryDto } from '../dto/user-query.dto'
import { UserResponseDto } from '../dto/user-response.dto'

import { UserValidationService } from './user-validation.service'
import { UserNotificationService } from './user-notification.service'

/**
 * 用户管理服务
 * 
 * 提供用户的CRUD操作和相关业务逻辑
 */
@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserProfile)
    private readonly profileRepository: Repository<UserProfile>,
    @InjectRepository(UserPreference)
    private readonly preferenceRepository: Repository<UserPreference>,
    @InjectRepository(UserPrivacy)
    private readonly privacyRepository: Repository<UserPrivacy>,
    @InjectRepository(UserEducation)
    private readonly educationRepository: Repository<UserEducation>,
    private readonly configService: ConfigService,
    private readonly validationService: UserValidationService,
    private readonly notificationService: UserNotificationService
  ) {}

  /**
   * 创建新用户
   */
  async create(createUserDto: CreateUserDto): Promise<UserResponseDto> {
    // 验证输入数据
    await this.validationService.validateCreateUser(createUserDto)

    // 检查手机号或邮箱是否已存在
    await this.checkUserExists(createUserDto.phone, createUserDto.email)

    // 创建用户实体
    const user = this.userRepository.create({
      ...createUserDto,
      status: UserStatus.PENDING,
      phoneVerified: false,
      emailVerified: false,
      loginCount: 0,
      termsAccepted: createUserDto.termsAccepted || false,
      termsAcceptedAt: createUserDto.termsAccepted ? new Date() : undefined
    })

    // 保存用户
    const savedUser = await this.userRepository.save(user)

    // 创建关联的默认设置
    await this.createDefaultUserSettings(savedUser.id)

    // 发送欢迎通知
    await this.notificationService.sendWelcomeNotification(savedUser)

    return this.mapToResponseDto(savedUser)
  }

  /**
   * 根据ID查找用户
   */
  async findById(id: string, includeRelations = false): Promise<UserResponseDto> {
    const relations = includeRelations ? ['profile', 'preference', 'privacy', 'educations'] : []
    
    const user = await this.userRepository.findOne({
      where: { id },
      relations
    })

    if (!user) {
      throw new NotFoundException(`用户不存在: ${id}`)
    }

    return this.mapToResponseDto(user)
  }

  /**
   * 根据手机号查找用户
   */
  async findByPhone(phone: string, countryCode = '+86'): Promise<UserResponseDto | null> {
    const user = await this.userRepository.findOne({
      where: { phone, countryCode },
      relations: ['profile', 'preference', 'privacy']
    })

    return user ? this.mapToResponseDto(user) : null
  }

  /**
   * 根据邮箱查找用户
   */
  async findByEmail(email: string): Promise<UserResponseDto | null> {
    const user = await this.userRepository.findOne({
      where: { email },
      relations: ['profile', 'preference', 'privacy']
    })

    return user ? this.mapToResponseDto(user) : null
  }

  /**
   * 根据用户名查找用户
   */
  async findByUsername(username: string): Promise<UserResponseDto | null> {
    const user = await this.userRepository.findOne({
      where: { username },
      relations: ['profile', 'preference', 'privacy']
    })

    return user ? this.mapToResponseDto(user) : null
  }

  /**
   * 分页查询用户列表
   */
  async findMany(query: UserQueryDto): Promise<{
    users: UserResponseDto[]
    total: number
    page: number
    limit: number
  }> {
    const {
      page = 1,
      limit = 20,
      search,
      userType,
      status,
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = query

    const queryBuilder = this.userRepository.createQueryBuilder('user')
      .leftJoinAndSelect('user.profile', 'profile')

    // 搜索条件
    if (search) {
      queryBuilder.andWhere(
        '(user.displayName LIKE :search OR user.username LIKE :search OR profile.realName LIKE :search)',
        { search: `%${search}%` }
      )
    }

    // 用户类型过滤
    if (userType) {
      queryBuilder.andWhere('user.userType = :userType', { userType })
    }

    // 状态过滤
    if (status) {
      queryBuilder.andWhere('user.status = :status', { status })
    }

    // 排序
    queryBuilder.orderBy(`user.${sortBy}`, sortOrder)

    // 分页
    const offset = (page - 1) * limit
    queryBuilder.skip(offset).take(limit)

    const [users, total] = await queryBuilder.getManyAndCount()

    return {
      users: users.map(user => this.mapToResponseDto(user)),
      total,
      page,
      limit
    }
  }

  /**
   * 更新用户信息
   */
  async update(id: string, updateUserDto: UpdateUserDto): Promise<UserResponseDto> {
    const user = await this.userRepository.findOne({ where: { id } })
    
    if (!user) {
      throw new NotFoundException(`用户不存在: ${id}`)
    }

    // 验证更新数据
    await this.validationService.validateUpdateUser(updateUserDto, user)

    // 检查手机号或邮箱冲突
    if (updateUserDto.phone && updateUserDto.phone !== user.phone) {
      await this.checkPhoneExists(updateUserDto.phone, updateUserDto.countryCode || user.countryCode, id)
    }

    if (updateUserDto.email && updateUserDto.email !== user.email) {
      await this.checkEmailExists(updateUserDto.email, id)
    }

    // 更新用户信息
    Object.assign(user, updateUserDto)
    const updatedUser = await this.userRepository.save(user)

    return this.mapToResponseDto(updatedUser)
  }

  /**
   * 删除用户（软删除）
   */
  async remove(id: string): Promise<void> {
    const user = await this.userRepository.findOne({ where: { id } })
    
    if (!user) {
      throw new NotFoundException(`用户不存在: ${id}`)
    }

    // 更新状态为非活跃而不是物理删除
    user.status = UserStatus.INACTIVE
    await this.userRepository.save(user)

    // 发送账户停用通知
    await this.notificationService.sendAccountDeactivationNotification(user)
  }

  /**
   * 激活用户账户
   */
  async activate(id: string): Promise<UserResponseDto> {
    const user = await this.userRepository.findOne({ where: { id } })
    
    if (!user) {
      throw new NotFoundException(`用户不存在: ${id}`)
    }

    user.status = UserStatus.ACTIVE
    const updatedUser = await this.userRepository.save(user)

    // 发送账户激活通知
    await this.notificationService.sendAccountActivationNotification(updatedUser)

    return this.mapToResponseDto(updatedUser)
  }

  /**
   * 验证手机号
   */
  async verifyPhone(id: string): Promise<UserResponseDto> {
    const user = await this.userRepository.findOne({ where: { id } })
    
    if (!user) {
      throw new NotFoundException(`用户不存在: ${id}`)
    }

    user.phoneVerified = true
    if (user.status === UserStatus.PENDING) {
      user.status = UserStatus.ACTIVE
    }

    const updatedUser = await this.userRepository.save(user)
    return this.mapToResponseDto(updatedUser)
  }

  /**
   * 验证邮箱
   */
  async verifyEmail(id: string): Promise<UserResponseDto> {
    const user = await this.userRepository.findOne({ where: { id } })
    
    if (!user) {
      throw new NotFoundException(`用户不存在: ${id}`)
    }

    user.emailVerified = true
    const updatedUser = await this.userRepository.save(user)
    return this.mapToResponseDto(updatedUser)
  }

  /**
   * 更新最后登录时间
   */
  async updateLastLogin(id: string): Promise<void> {
    await this.userRepository.update(id, {
      lastLoginAt: new Date(),
      lastActiveAt: new Date(),
      loginCount: () => 'loginCount + 1'
    })
  }

  /**
   * 更新最后活跃时间
   */
  async updateLastActive(id: string): Promise<void> {
    await this.userRepository.update(id, {
      lastActiveAt: new Date()
    })
  }

  /**
   * 检查用户是否存在
   */
  private async checkUserExists(phone?: string, email?: string): Promise<void> {
    if (phone) {
      await this.checkPhoneExists(phone)
    }
    if (email) {
      await this.checkEmailExists(email)
    }
  }

  /**
   * 检查手机号是否已存在
   */
  private async checkPhoneExists(phone: string, countryCode = '+86', excludeUserId?: string): Promise<void> {
    const where: FindOptionsWhere<User> = { phone, countryCode }
    if (excludeUserId) {
      where.id = { $ne: excludeUserId } as any
    }

    const existingUser = await this.userRepository.findOne({ where })
    if (existingUser) {
      throw new ConflictException('手机号已被注册')
    }
  }

  /**
   * 检查邮箱是否已存在
   */
  private async checkEmailExists(email: string, excludeUserId?: string): Promise<void> {
    const where: FindOptionsWhere<User> = { email }
    if (excludeUserId) {
      where.id = { $ne: excludeUserId } as any
    }

    const existingUser = await this.userRepository.findOne({ where })
    if (existingUser) {
      throw new ConflictException('邮箱已被注册')
    }
  }

  /**
   * 创建用户默认设置
   */
  private async createDefaultUserSettings(userId: string): Promise<void> {
    // 创建默认资料
    const profile = this.profileRepository.create({
      userId,
      timezone: 'Asia/Shanghai',
      language: 'zh-CN',
      isPublic: false
    })
    await this.profileRepository.save(profile)

    // 创建默认偏好设置
    const preference = this.preferenceRepository.create({
      userId,
      language: 'zh-CN',
      timezone: 'Asia/Shanghai'
    })
    await this.preferenceRepository.save(preference)

    // 创建默认隐私设置
    const privacy = this.privacyRepository.create({
      userId
    })
    await this.privacyRepository.save(privacy)
  }

  /**
   * 将用户实体映射为响应DTO
   */
  private mapToResponseDto(user: User): UserResponseDto {
    return {
      id: user.id,
      username: user.username,
      phone: user.phone,
      countryCode: user.countryCode,
      email: user.email,
      displayName: user.displayName,
      avatarUrl: user.avatarUrl,
      status: user.status,
      userType: user.userType,
      phoneVerified: user.phoneVerified,
      emailVerified: user.emailVerified,
      lastLoginAt: user.lastLoginAt,
      lastActiveAt: user.lastActiveAt,
      loginCount: user.loginCount,
      termsAccepted: user.termsAccepted,
      termsAcceptedAt: user.termsAcceptedAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      profile: user.profile,
      preference: user.preference,
      privacy: user.privacy,
      educations: user.educations
    }
  }
}
