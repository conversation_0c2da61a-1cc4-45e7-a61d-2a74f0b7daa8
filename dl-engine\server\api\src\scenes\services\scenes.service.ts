import { Injectable, NotFoundException, ForbiddenException, ConflictException } from '@nestjs/common'
import { InjectRepository } from '@nestjs/typeorm'
import { Repository, FindOptionsWhere, Like, In } from 'typeorm'
import { ConfigService } from '@nestjs/config'

import { Scene, SceneStatus, SceneType, RenderQuality } from '../entities/scene.entity'
import { SceneVersion } from '../entities/scene-version.entity'
import { SceneComponent } from '../entities/scene-component.entity'

import { CreateSceneDto } from '../dto/create-scene.dto'
import { UpdateSceneDto } from '../dto/update-scene.dto'
import { SceneQueryDto } from '../dto/scene-query.dto'
import { SceneResponseDto } from '../dto/scene-response.dto'

import { SceneValidationService } from './scene-validation.service'
import { SceneRenderingService } from './scene-rendering.service'
import { ProjectsService } from '../../projects/services/projects.service'

/**
 * 场景管理服务
 * 
 * 提供3D场景的CRUD操作和相关业务逻辑
 */
@Injectable()
export class ScenesService {
  constructor(
    @InjectRepository(Scene)
    private readonly sceneRepository: Repository<Scene>,
    @InjectRepository(SceneVersion)
    private readonly versionRepository: Repository<SceneVersion>,
    @InjectRepository(SceneComponent)
    private readonly componentRepository: Repository<SceneComponent>,
    private readonly configService: ConfigService,
    private readonly validationService: SceneValidationService,
    private readonly renderingService: SceneRenderingService,
    private readonly projectsService: ProjectsService
  ) {}

  /**
   * 创建新场景
   */
  async create(createSceneDto: CreateSceneDto, userId: string): Promise<SceneResponseDto> {
    // 验证输入数据
    await this.validationService.validateCreateScene(createSceneDto, userId)

    // 检查项目访问权限
    const project = await this.projectsService.findById(createSceneDto.projectId, userId)
    if (!project) {
      throw new NotFoundException('项目不存在')
    }

    // 检查场景名称是否重复（同一项目下）
    await this.checkSceneNameExists(createSceneDto.name, createSceneDto.projectId)

    // 创建默认配置
    const defaultConfiguration = this.createDefaultConfiguration()
    const defaultEnvironment = this.createDefaultEnvironment()

    // 创建场景实体
    const scene = this.sceneRepository.create({
      ...createSceneDto,
      creatorId: userId,
      status: SceneStatus.DRAFT,
      version: '1.0.0',
      configuration: defaultConfiguration,
      environment: defaultEnvironment,
      statistics: {
        views: 0,
        downloads: 0,
        shares: 0,
        likes: 0,
        comments: 0,
        bookmarks: 0,
        forks: 0,
        avgRating: 0,
        totalRatings: 0,
        lastActivity: new Date()
      }
    })

    // 保存场景
    const savedScene = await this.sceneRepository.save(scene)

    // 创建初始版本
    await this.createInitialVersion(savedScene.id, userId)

    // 生成缩略图
    await this.generateThumbnail(savedScene.id)

    return this.mapToResponseDto(savedScene)
  }

  /**
   * 根据ID查找场景
   */
  async findById(id: string, userId?: string, includeRelations = false): Promise<SceneResponseDto> {
    const relations = includeRelations 
      ? ['project', 'creator', 'versions', 'components', 'assets', 'shares', 'comments', 'bookmarks']
      : ['project', 'creator']
    
    const scene = await this.sceneRepository.findOne({
      where: { id },
      relations
    })

    if (!scene) {
      throw new NotFoundException(`场景不存在: ${id}`)
    }

    // 检查访问权限
    if (userId) {
      await this.checkSceneAccess(scene, userId, 'read')
      
      // 更新访问统计
      scene.updateStatistics('views')
      await this.sceneRepository.save(scene)
    }

    return this.mapToResponseDto(scene)
  }

  /**
   * 分页查询场景列表
   */
  async findMany(query: SceneQueryDto, userId?: string): Promise<{
    scenes: SceneResponseDto[]
    total: number
    page: number
    limit: number
  }> {
    const {
      page = 1,
      limit = 20,
      search,
      type,
      status,
      projectId,
      creatorId,
      sortBy = 'updatedAt',
      sortOrder = 'DESC'
    } = query

    const queryBuilder = this.sceneRepository.createQueryBuilder('scene')
      .leftJoinAndSelect('scene.project', 'project')
      .leftJoinAndSelect('scene.creator', 'creator')

    // 权限过滤
    if (userId) {
      queryBuilder.leftJoin('project.permissions', 'permission', 'permission.userId = :userId', { userId })
      queryBuilder.andWhere(
        '(project.visibility = :publicVisibility OR project.ownerId = :userId OR permission.userId IS NOT NULL)',
        { publicVisibility: 'public', userId }
      )
    } else {
      // 未登录用户只能看到公开项目的场景
      queryBuilder.andWhere('project.visibility = :publicVisibility', { 
        publicVisibility: 'public' 
      })
    }

    // 状态过滤（排除已删除的场景）
    queryBuilder.andWhere('scene.status != :deletedStatus', { 
      deletedStatus: SceneStatus.DELETED 
    })

    // 搜索条件
    if (search) {
      queryBuilder.andWhere(
        '(scene.name LIKE :search OR scene.description LIKE :search)',
        { search: `%${search}%` }
      )
    }

    // 场景类型过滤
    if (type) {
      queryBuilder.andWhere('scene.type = :type', { type })
    }

    // 状态过滤
    if (status) {
      queryBuilder.andWhere('scene.status = :status', { status })
    }

    // 项目过滤
    if (projectId) {
      queryBuilder.andWhere('scene.projectId = :projectId', { projectId })
    }

    // 创建者过滤
    if (creatorId) {
      queryBuilder.andWhere('scene.creatorId = :creatorId', { creatorId })
    }

    // 排序
    queryBuilder.orderBy(`scene.${sortBy}`, sortOrder)

    // 分页
    const offset = (page - 1) * limit
    queryBuilder.skip(offset).take(limit)

    const [scenes, total] = await queryBuilder.getManyAndCount()

    return {
      scenes: scenes.map(scene => this.mapToResponseDto(scene)),
      total,
      page,
      limit
    }
  }

  /**
   * 获取项目的场景列表
   */
  async findByProject(projectId: string, userId?: string): Promise<SceneResponseDto[]> {
    // 检查项目访问权限
    if (userId) {
      await this.projectsService.findById(projectId, userId)
    }

    const scenes = await this.sceneRepository.find({
      where: { 
        projectId,
        status: In([SceneStatus.DRAFT, SceneStatus.PUBLISHED, SceneStatus.ARCHIVED])
      },
      relations: ['creator'],
      order: { updatedAt: 'DESC' }
    })

    return scenes.map(scene => this.mapToResponseDto(scene))
  }

  /**
   * 更新场景信息
   */
  async update(id: string, updateSceneDto: UpdateSceneDto, userId: string): Promise<SceneResponseDto> {
    const scene = await this.sceneRepository.findOne({ 
      where: { id },
      relations: ['project', 'creator']
    })
    
    if (!scene) {
      throw new NotFoundException(`场景不存在: ${id}`)
    }

    // 检查编辑权限
    await this.checkSceneAccess(scene, userId, 'write')

    // 验证更新数据
    await this.validationService.validateUpdateScene(updateSceneDto, scene)

    // 检查场景名称冲突
    if (updateSceneDto.name && updateSceneDto.name !== scene.name) {
      await this.checkSceneNameExists(updateSceneDto.name, scene.projectId, id)
    }

    // 记录变更前的状态
    const oldStatus = scene.status

    // 更新场景信息
    Object.assign(scene, updateSceneDto)

    // 如果配置或环境发生变化，重新生成缩略图
    if (updateSceneDto.configuration || updateSceneDto.environment) {
      await this.generateThumbnail(scene.id)
    }

    const updatedScene = await this.sceneRepository.save(scene)

    // 如果状态发生变化，更新发布时间
    if (updateSceneDto.status && updateSceneDto.status !== oldStatus) {
      if (updateSceneDto.status === SceneStatus.PUBLISHED) {
        updatedScene.publishedAt = new Date()
      } else if (updateSceneDto.status === SceneStatus.ARCHIVED) {
        updatedScene.archivedAt = new Date()
      }
      await this.sceneRepository.save(updatedScene)
    }

    return this.mapToResponseDto(updatedScene)
  }

  /**
   * 删除场景（软删除）
   */
  async remove(id: string, userId: string): Promise<void> {
    const scene = await this.sceneRepository.findOne({ 
      where: { id },
      relations: ['project', 'creator']
    })
    
    if (!scene) {
      throw new NotFoundException(`场景不存在: ${id}`)
    }

    // 检查删除权限
    await this.checkSceneAccess(scene, userId, 'delete')

    // 软删除场景
    scene.status = SceneStatus.DELETED
    await this.sceneRepository.save(scene)
  }

  /**
   * 发布场景
   */
  async publish(id: string, userId: string): Promise<SceneResponseDto> {
    const scene = await this.sceneRepository.findOne({ 
      where: { id },
      relations: ['project', 'creator']
    })
    
    if (!scene) {
      throw new NotFoundException(`场景不存在: ${id}`)
    }

    // 检查发布权限
    await this.checkSceneAccess(scene, userId, 'publish')

    // 检查场景是否可以发布
    if (scene.status === SceneStatus.PUBLISHED) {
      throw new ConflictException('场景已经是发布状态')
    }

    if (scene.status === SceneStatus.DELETED) {
      throw new ConflictException('已删除的场景无法发布')
    }

    // 验证场景完整性
    await this.validationService.validateSceneForPublish(scene)

    // 更新场景状态
    scene.status = SceneStatus.PUBLISHED
    scene.publishedAt = new Date()

    const updatedScene = await this.sceneRepository.save(scene)

    // 生成高质量预览图
    await this.generatePreviewImages(scene.id)

    return this.mapToResponseDto(updatedScene)
  }

  /**
   * 复制场景
   */
  async duplicate(id: string, userId: string, newName?: string): Promise<SceneResponseDto> {
    const originalScene = await this.sceneRepository.findOne({ 
      where: { id },
      relations: ['project', 'creator', 'components', 'assets']
    })
    
    if (!originalScene) {
      throw new NotFoundException(`场景不存在: ${id}`)
    }

    // 检查访问权限
    await this.checkSceneAccess(originalScene, userId, 'read')

    // 生成新场景名称
    const duplicateName = newName || `${originalScene.name} (副本)`
    await this.checkSceneNameExists(duplicateName, originalScene.projectId)

    // 创建新场景
    const duplicatedScene = this.sceneRepository.create({
      ...originalScene,
      id: undefined,
      name: duplicateName,
      creatorId: userId,
      status: SceneStatus.DRAFT,
      publishedAt: null,
      archivedAt: null,
      statistics: {
        views: 0,
        downloads: 0,
        shares: 0,
        likes: 0,
        comments: 0,
        bookmarks: 0,
        forks: 0,
        avgRating: 0,
        totalRatings: 0,
        lastActivity: new Date()
      }
    })

    const savedScene = await this.sceneRepository.save(duplicatedScene)

    // 复制场景组件
    if (originalScene.components) {
      for (const component of originalScene.components) {
        const duplicatedComponent = this.componentRepository.create({
          ...component,
          id: undefined,
          sceneId: savedScene.id
        })
        await this.componentRepository.save(duplicatedComponent)
      }
    }

    // 更新原场景的复制统计
    originalScene.updateStatistics('forks')
    await this.sceneRepository.save(originalScene)

    return this.mapToResponseDto(savedScene)
  }

  /**
   * 渲染场景预览
   */
  async renderPreview(id: string, options: {
    width?: number
    height?: number
    quality?: RenderQuality
    camera?: any
  } = {}): Promise<string> {
    const scene = await this.sceneRepository.findOne({ where: { id } })
    
    if (!scene) {
      throw new NotFoundException(`场景不存在: ${id}`)
    }

    return await this.renderingService.renderScenePreview(scene, options)
  }

  /**
   * 检查场景名称是否存在
   */
  private async checkSceneNameExists(name: string, projectId: string, excludeId?: string): Promise<void> {
    const where: FindOptionsWhere<Scene> = { 
      name, 
      projectId,
      status: In([SceneStatus.DRAFT, SceneStatus.PUBLISHED, SceneStatus.ARCHIVED])
    }
    
    if (excludeId) {
      where.id = { $ne: excludeId } as any
    }

    const existingScene = await this.sceneRepository.findOne({ where })
    if (existingScene) {
      throw new ConflictException('场景名称已存在')
    }
  }

  /**
   * 检查场景访问权限
   */
  private async checkSceneAccess(scene: Scene, userId: string, action: string): Promise<void> {
    // 场景创建者拥有所有权限
    if (scene.creatorId === userId) return

    // 检查项目权限
    try {
      await this.projectsService.findById(scene.projectId, userId)
    } catch (error) {
      throw new ForbiddenException(`无权限访问此场景: ${action}`)
    }
  }

  /**
   * 创建默认配置
   */
  private createDefaultConfiguration() {
    return {
      rendering: {
        quality: RenderQuality.MEDIUM,
        shadows: true,
        reflections: false,
        antiAliasing: true,
        postProcessing: false,
        maxLights: 8,
        maxTextures: 32
      },
      physics: {
        enabled: false,
        gravity: { x: 0, y: -9.81, z: 0 },
        timeStep: 1/60,
        maxSubSteps: 3
      },
      audio: {
        enabled: true,
        masterVolume: 1.0,
        spatialAudio: true,
        maxSources: 16
      },
      interaction: {
        enabled: true,
        multiTouch: true,
        gestureRecognition: false,
        voiceControl: false
      },
      xr: {
        vrEnabled: false,
        arEnabled: false,
        handTracking: false,
        eyeTracking: false,
        roomScale: false
      }
    }
  }

  /**
   * 创建默认环境
   */
  private createDefaultEnvironment() {
    return {
      skybox: {
        type: 'color' as const,
        color: '#87CEEB',
        rotation: 0,
        exposure: 1.0
      },
      lighting: {
        ambientLight: {
          color: '#404040',
          intensity: 0.4
        },
        directionalLight: {
          color: '#ffffff',
          intensity: 1.0,
          direction: { x: -1, y: -1, z: -1 },
          castShadows: true
        }
      },
      postProcessing: {
        bloom: false,
        toneMappingExposure: 1.0,
        colorGrading: {
          contrast: 1.0,
          brightness: 0.0,
          saturation: 1.0,
          hue: 0.0
        }
      }
    }
  }

  /**
   * 创建初始版本
   */
  private async createInitialVersion(sceneId: string, userId: string): Promise<void> {
    const version = this.versionRepository.create({
      sceneId,
      version: '1.0.0',
      name: '初始版本',
      description: '场景的初始版本',
      createdById: userId,
      isCurrent: true,
      status: 'draft' as any,
      type: 'minor' as any
    })

    await this.versionRepository.save(version)
  }

  /**
   * 生成缩略图
   */
  private async generateThumbnail(sceneId: string): Promise<void> {
    try {
      const thumbnailUrl = await this.renderingService.generateThumbnail(sceneId)
      await this.sceneRepository.update(sceneId, { thumbnailUrl })
    } catch (error) {
      console.error(`Failed to generate thumbnail for scene ${sceneId}:`, error)
    }
  }

  /**
   * 生成预览图
   */
  private async generatePreviewImages(sceneId: string): Promise<void> {
    try {
      const previewImages = await this.renderingService.generatePreviewImages(sceneId)
      await this.sceneRepository.update(sceneId, { previewImages })
    } catch (error) {
      console.error(`Failed to generate preview images for scene ${sceneId}:`, error)
    }
  }

  /**
   * 将场景实体映射为响应DTO
   */
  private mapToResponseDto(scene: Scene): SceneResponseDto {
    return {
      id: scene.id,
      name: scene.name,
      description: scene.description,
      type: scene.type,
      status: scene.status,
      projectId: scene.projectId,
      creatorId: scene.creatorId,
      thumbnailUrl: scene.thumbnailUrl,
      previewImages: scene.previewImages,
      sceneDataPath: scene.sceneDataPath,
      size: scene.size,
      version: scene.version,
      configuration: scene.configuration,
      environment: scene.environment,
      metadata: scene.metadata,
      statistics: scene.statistics,
      lastRenderedAt: scene.lastRenderedAt,
      publishedAt: scene.publishedAt,
      archivedAt: scene.archivedAt,
      createdAt: scene.createdAt,
      updatedAt: scene.updatedAt,
      project: scene.project,
      creator: scene.creator,
      versions: scene.versions,
      components: scene.components,
      assets: scene.assets,
      shares: scene.shares,
      comments: scene.comments,
      bookmarks: scene.bookmarks
    }
  }
}
